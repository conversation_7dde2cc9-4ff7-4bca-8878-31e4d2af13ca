import { asyncHandler } from '../utils/asyncHandler.js';
import { ApiResponse } from '../utils/ApiResponse.js';
import { ApiError } from '../utils/ApiError.js';
import { uploadOnCloudinary } from '../utils/cloudinary.js';
import {Video} from '../models/video.model.js';
// uplaoading new video
const uploadVideo = asyncHandler( async(req,res)=>{
    const {title , description} = req.body;
    if(!title || ! description){
        throw new ApiError(400,"Title and description are required")
    }
    const owner = req.user._id;
    const videoFile = req.files?.videoFile?.[0]?.path;
    const thumbnail = req.files?.thumbnail?.[0]?.path;

    if(!videoFile || !thumbnail){
        throw new ApiError(400,"Video and thumbnail are required")
    }
    const uploadedVideo = await uploadOnCloudinary(videoFile);
    const uploadedThumbnail = await uploadOnCloudinary(thumbnail);

    const duration = uploadedVideo.duration; // Duration in seconds

    const video = await Video.create({
        title,
        description,
        videoFile:uploadedVideo.url,
        thumbnail:uploadedThumbnail.url,
        duration,
        owner
    })
    if(!video){
        throw new ApiError(500,"Something went wrong while uploading video")
    }
    return res.status(201).json(new ApiResponse(200,video,"Video uploaded successfully"))
})

export {uploadVideo}