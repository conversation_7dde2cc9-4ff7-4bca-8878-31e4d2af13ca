import { Router } from "express";
import {upload} from '../middlewares/multer.middleware.js'
import { verifyJwt } from "../middlewares/auth.middleware.js"
import { uploadVideo } from "../controller/video.controller.js";

const router = Router();

router.route("/uploadVideo").post(
    upload.fields([
            {
                name:"videoFile",
                maxCount:1
            },
            {
                name:"thumbnail",
                maxCount:1
            }
        ]),
    verifyJwt,
    uploadVideo
)

export default router