import { Router } from "express";
import { upload } from '../middlewares/multer.middleware.js';
import { verifyJwt } from "../middlewares/auth.middleware.js";
import {
    createChannel,
    getUserChannels,
    getChannelByHandle,
    getChannelById,
    updateChannel,
    updateChannelAvatar,
    updateChannelBanner,
    deleteChannel,
    getChannelVideos,
    getChannelAnalytics
} from "../controller/channel.controller.js";

const router = Router();

// Public routes
router.route("/handle/:handle").get(getChannelByHandle);
// router.route("/:channelId").get(getChannelById);
router.route("/:channelId/videos").get(getChannelVideos);

// Protected routes (require authentication)
router.route("/").post(verifyJwt, createChannel);
router.route("/user/channels").get(verifyJwt, getUserChannels);
router.route("/:channelId/update").put(verifyJwt, updateChannel);
router.route("/:channelId/avatar").put(verifyJwt, upload.single("avatar"), updateChannelAvatar);
router.route("/:channelId/banner").put(verifyJwt, upload.single("banner"), updateChannelBanner);
router.route("/:channelId/delete").delete(verifyJwt, deleteChannel);
router.route("/:channelId/analytics").get(verifyJwt, getChannelAnalytics);

export default router;
