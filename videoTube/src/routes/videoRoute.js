import { Router } from "express";
import { upload } from '../middlewares/multer.middleware.js';
import { verifyJwt } from "../middlewares/auth.middleware.js";
import {
    uploadVideo,
    getAllVideos,
    getVideoById,
    updateVideo,
    deleteVideo,
    getUserVideos,
    getVideosByCategory
} from "../controller/video.controller.js";

const router = Router();

// Public routes
// router.route("/").get(getAllVideos);
// router.route("/:videoId").get(getVideoById);
// router.route("/category/:category").get(getVideosByCategory);

router.route("/user/:userId").get(getUserVideos);

// Protected routes (require authentication)
router.route("/upload").post(
    verifyJwt,
    upload.fields([
        {
            name: "videoFile",
            maxCount: 1
        },
        {
            name: "thumbnail",
            maxCount: 1
        }
    ]),
    uploadVideo
);

router.route("/:videoId/update").put(verifyJwt, updateVideo);
router.route("/:videoId/delete").delete(verifyJwt, deleteVideo);

export default router;
