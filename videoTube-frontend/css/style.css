/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>o', Arial, sans-serif;
    background-color: #0f0f0f;
    color: #ffffff;
    line-height: 1.6;
}

/* Navigation Header */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: #212121;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    z-index: 1000;
    border-bottom: 1px solid #303030;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.menu-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
}

.menu-btn:hover {
    background-color: #303030;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
    font-weight: bold;
    color: #ff0000;
}

.logo i {
    font-size: 24px;
}

.nav-center {
    flex: 1;
    max-width: 640px;
    margin: 0 40px;
}

.search-container {
    display: flex;
    height: 40px;
}

.search-input {
    flex: 1;
    background-color: #121212;
    border: 1px solid #303030;
    border-right: none;
    color: #ffffff;
    padding: 0 16px;
    font-size: 16px;
    border-radius: 20px 0 0 20px;
}

.search-input:focus {
    outline: none;
    border-color: #065fd4;
}

.search-btn {
    width: 64px;
    background-color: #303030;
    border: 1px solid #303030;
    color: #ffffff;
    cursor: pointer;
    border-radius: 0 20px 20px 0;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background-color: #404040;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-icon {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
}

.nav-icon:hover {
    background-color: #303030;
}

.user-menu {
    position: relative;
}

.user-avatar {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-avatar:hover {
    background-color: #303030;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #282828;
    border: 1px solid #404040;
    border-radius: 8px;
    min-width: 240px;
    padding: 8px 0;
    display: none;
    z-index: 1001;
}

.dropdown-menu.show {
    display: block;
}

.user-info {
    padding: 16px;
    border-bottom: 1px solid #404040;
}

.user-avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-bottom: 8px;
}

.user-details span {
    display: block;
    font-size: 14px;
}

.user-details span:first-child {
    font-weight: bold;
    margin-bottom: 4px;
}

.user-details span:last-child {
    color: #aaaaaa;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    color: #ffffff;
    text-decoration: none;
    transition: background-color 0.2s;
}

.dropdown-menu a:hover {
    background-color: #404040;
}

.menu-divider {
    height: 1px;
    background-color: #404040;
    margin: 8px 0;
}

.login-btn {
    background-color: transparent;
    border: 1px solid #065fd4;
    color: #065fd4;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.login-btn:hover {
    background-color: #065fd4;
    color: #ffffff;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 56px;
    left: 0;
    width: 240px;
    height: calc(100vh - 56px);
    background-color: #212121;
    overflow-y: auto;
    padding: 12px 0;
    z-index: 999;
    transition: transform 0.3s ease;
}

.sidebar.collapsed {
    transform: translateX(-240px);
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
}

.nav-section {
    margin-bottom: 24px;
}

.nav-section h3 {
    padding: 8px 24px;
    font-size: 14px;
    font-weight: 500;
    color: #aaaaaa;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 10px 24px;
    color: #ffffff;
    text-decoration: none;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background-color: #303030;
}

.nav-item.active {
    background-color: #303030;
    border-right: 3px solid #ff0000;
}

.nav-item i {
    width: 24px;
    font-size: 20px;
}

/* Main Content */
.main-content {
    margin-left: 240px;
    margin-top: 56px;
    padding: 24px;
    min-height: calc(100vh - 56px);
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 0;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Video Grid */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
}

.video-card {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.video-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.video-info {
    padding: 12px;
}

.video-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-channel {
    color: #aaaaaa;
    font-size: 14px;
    margin-bottom: 4px;
}

.video-meta {
    color: #aaaaaa;
    font-size: 14px;
}

/* Loading */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #aaaaaa;
}

.loading i {
    font-size: 32px;
    margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-240px);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .nav-center {
        margin: 0 16px;
    }
    
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 0 8px;
    }
    
    .nav-center {
        margin: 0 8px;
    }
    
    .main-content {
        padding: 16px 8px;
    }
    
    .video-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}
